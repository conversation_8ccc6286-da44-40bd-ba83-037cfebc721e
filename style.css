* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 20px 30px;
    margin-bottom: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

header h1 {
    color: #2c3e50;
    font-size: 2rem;
    font-weight: 600;
}

header h1 i {
    color: #e74c3c;
    margin-right: 10px;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 500;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #95a5a6;
    animation: pulse 2s infinite;
}

.status-dot.active {
    background: #27ae60;
}

.status-dot.inactive {
    background: #e74c3c;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 25px;
}

.card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.card h2 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 1.3rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.card h2 i {
    color: #3498db;
}

.stats-card {
    grid-column: span 2;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.stat-item {
    text-align: center;
    padding: 20px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 12px;
    border-left: 4px solid #3498db;
}

.stat-value {
    font-size: 2.5rem;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 5px;
}

.stat-label {
    color: #7f8c8d;
    font-size: 0.9rem;
    font-weight: 500;
}

.system-info {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #ecf0f1;
}

.info-row:last-child {
    border-bottom: none;
}

.info-label {
    font-weight: 600;
    color: #34495e;
}

.info-value {
    color: #2c3e50;
    font-weight: 500;
}

.list-container {
    max-height: 300px;
    overflow-y: auto;
}

.list-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #ecf0f1;
}

.list-item:last-child {
    border-bottom: none;
}

.domain {
    color: #2c3e50;
    font-weight: 500;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.count {
    color: #e74c3c;
    font-weight: bold;
    background: #ffeaa7;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 0.9rem;
}

.chart-card {
    grid-column: span 2;
}

.chart-container {
    height: 300px;
    position: relative;
}

.query-types {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.query-type-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 8px;
    border-left: 4px solid #9b59b6;
}

.type-label {
    font-weight: 500;
    color: #2c3e50;
}

.type-count {
    font-weight: bold;
    color: #8e44ad;
}

.loading, .no-data {
    text-align: center;
    color: #7f8c8d;
    font-style: italic;
    padding: 20px;
}

/* Scrollbar personalizada */
.list-container::-webkit-scrollbar {
    width: 6px;
}

.list-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.list-container::-webkit-scrollbar-thumb {
    background: #bdc3c7;
    border-radius: 3px;
}

.list-container::-webkit-scrollbar-thumb:hover {
    background: #95a5a6;
}

/* Responsividade */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    header h1 {
        font-size: 1.5rem;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-card,
    .chart-card {
        grid-column: span 1;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .stat-value {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .card {
        padding: 20px;
    }
    
    .stat-value {
        font-size: 1.8rem;
    }
}
