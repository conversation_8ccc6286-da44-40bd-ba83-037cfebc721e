<?php
// Teste específico do sistema de autenticação do Pi-hole v6+
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Teste de Autenticação Pi-hole v6+</h1>";

$pihole_ip = '************';
$password = 'MAFHUcZbmK4ulb42wIXPUUd+hjTv1pTMgqduUfG7daI=';
$auth_url = "http://$pihole_ip/api/auth";

echo "<h2>Informações:</h2>";
echo "Pi-hole IP: $pihole_ip<br>";
echo "Auth URL: $auth_url<br>";
echo "Password length: " . strlen($password) . " caracteres<br>";
echo "cURL disponível: " . (function_exists('curl_init') ? 'Sim' : 'Não') . "<br>";

echo "<h2>Teste 1: Login direto</h2>";

$loginData = ['password' => $password];

$ch = curl_init();
curl_setopt_array($ch, [
    CURLOPT_URL => $auth_url,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_POST => true,
    CURLOPT_POSTFIELDS => json_encode($loginData),
    CURLOPT_TIMEOUT => 15,
    CURLOPT_CONNECTTIMEOUT => 10,
    CURLOPT_HTTPHEADER => [
        'Content-Type: application/json',
        'Accept: application/json'
    ],
    CURLOPT_VERBOSE => true
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "Status HTTP: $httpCode<br>";

if ($response === false || !empty($error)) {
    echo "❌ Erro cURL: " . htmlspecialchars($error) . "<br>";
} else {
    echo "✅ Resposta recebida<br>";
    echo "Resposta bruta: " . htmlspecialchars($response) . "<br>";
    
    $loginResult = json_decode($response, true);
    if ($loginResult && json_last_error() === JSON_ERROR_NONE) {
        echo "✅ JSON válido<br>";
        echo "<pre>" . htmlspecialchars(json_encode($loginResult, JSON_PRETTY_PRINT)) . "</pre>";
        
        if (isset($loginResult['session']) && isset($loginResult['session']['sid'])) {
            $sessionId = $loginResult['session']['sid'];
            echo "✅ Session ID obtido: " . htmlspecialchars(substr($sessionId, 0, 20)) . "...<br>";
            
            // Teste 2: Usar o session ID para acessar um endpoint
            echo "<h2>Teste 2: Usando Session ID</h2>";
            
            $testUrl = "http://$pihole_ip/api/dns/blocking";
            echo "Testando URL: $testUrl<br>";
            
            $ch2 = curl_init();
            curl_setopt_array($ch2, [
                CURLOPT_URL => $testUrl,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_TIMEOUT => 10,
                CURLOPT_HTTPHEADER => [
                    'Accept: application/json',
                    'X-Pi-hole-Authenticate: ' . $sessionId
                ]
            ]);
            
            $response2 = curl_exec($ch2);
            $httpCode2 = curl_getinfo($ch2, CURLINFO_HTTP_CODE);
            $error2 = curl_error($ch2);
            curl_close($ch2);
            
            echo "Status HTTP: $httpCode2<br>";
            
            if ($response2 === false || !empty($error2)) {
                echo "❌ Erro cURL: " . htmlspecialchars($error2) . "<br>";
            } else {
                echo "✅ Resposta recebida<br>";
                echo "Resposta: " . htmlspecialchars($response2) . "<br>";
                
                $data2 = json_decode($response2, true);
                if ($data2 && json_last_error() === JSON_ERROR_NONE) {
                    echo "✅ JSON válido<br>";
                    echo "<pre>" . htmlspecialchars(json_encode($data2, JSON_PRETTY_PRINT)) . "</pre>";
                } else {
                    echo "❌ JSON inválido: " . json_last_error_msg() . "<br>";
                }
            }
            
            // Teste 3: Testar endpoint de estatísticas
            echo "<h2>Teste 3: Endpoint de Estatísticas</h2>";
            
            $statsUrl = "http://$pihole_ip/api/stats/summary";
            echo "Testando URL: $statsUrl<br>";
            
            $ch3 = curl_init();
            curl_setopt_array($ch3, [
                CURLOPT_URL => $statsUrl,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_TIMEOUT => 10,
                CURLOPT_HTTPHEADER => [
                    'Accept: application/json',
                    'X-Pi-hole-Authenticate: ' . $sessionId
                ]
            ]);
            
            $response3 = curl_exec($ch3);
            $httpCode3 = curl_getinfo($ch3, CURLINFO_HTTP_CODE);
            $error3 = curl_error($ch3);
            curl_close($ch3);
            
            echo "Status HTTP: $httpCode3<br>";
            
            if ($response3 === false || !empty($error3)) {
                echo "❌ Erro cURL: " . htmlspecialchars($error3) . "<br>";
            } else {
                echo "✅ Resposta recebida<br>";
                echo "Resposta: " . htmlspecialchars(substr($response3, 0, 300)) . "...<br>";
                
                $data3 = json_decode($response3, true);
                if ($data3 && json_last_error() === JSON_ERROR_NONE) {
                    echo "✅ JSON válido<br>";
                    echo "<pre>" . htmlspecialchars(json_encode($data3, JSON_PRETTY_PRINT)) . "</pre>";
                } else {
                    echo "❌ JSON inválido: " . json_last_error_msg() . "<br>";
                }
            }
            
        } else {
            echo "❌ Session ID não encontrado na resposta<br>";
        }
    } else {
        echo "❌ JSON inválido: " . json_last_error_msg() . "<br>";
    }
}

echo "<h2>Teste 4: Nossa API Proxy</h2>";

$proxyUrl = "http://localhost/DashboardPihole/api.php?endpoint=test";
echo "Testando nossa API: $proxyUrl<br>";

$ch4 = curl_init();
curl_setopt_array($ch4, [
    CURLOPT_URL => $proxyUrl,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_TIMEOUT => 15
]);

$response4 = curl_exec($ch4);
$httpCode4 = curl_getinfo($ch4, CURLINFO_HTTP_CODE);
$error4 = curl_error($ch4);
curl_close($ch4);

echo "Status HTTP: $httpCode4<br>";

if ($response4 === false || !empty($error4)) {
    echo "❌ Erro: " . htmlspecialchars($error4) . "<br>";
} else {
    echo "✅ Resposta recebida<br>";
    echo "Resposta: " . htmlspecialchars(substr($response4, 0, 500)) . "...<br>";
    
    $data4 = json_decode($response4, true);
    if ($data4 && json_last_error() === JSON_ERROR_NONE) {
        echo "✅ JSON válido<br>";
        echo "<pre>" . htmlspecialchars(json_encode($data4, JSON_PRETTY_PRINT)) . "</pre>";
    } else {
        echo "❌ JSON inválido: " . json_last_error_msg() . "<br>";
    }
}
?>
