<?php
// Versão alternativa da API usando cURL
error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Configurações do Pi-hole
define('PIHOLE_IP', '************');
define('API_TOKEN', 'MAFHUcZbmK4ulb42wIXPUUd+hjTv1pTMgqduUfG7daI=');
define('PIHOLE_URL', 'http://' . PIHOLE_IP . '/api');

// Função para fazer requisições usando cURL
function fetchPiholeDataCurl($endpoint, $params = []) {
    // Nova estrutura da API: /api/endpoint
    $url = PIHOLE_URL . '/' . $endpoint;

    // Adicionar parâmetros se fornecidos
    $query_params = [];
    if (!empty($params)) {
        $query_params = array_merge($query_params, $params);
    }

    // Adicionar token para endpoints que precisam de autenticação
    $authEndpoints = ['topClients', 'topBlocked', 'queryTypesOverTime', 'overTimeDataClients', 'enable', 'disable'];
    if (in_array($endpoint, $authEndpoints) || $endpoint === 'summaryRaw') {
        $query_params['auth'] = API_TOKEN;
    }

    // Construir URL final
    if (!empty($query_params)) {
        $url .= '?' . http_build_query($query_params);
    }
    
    $ch = curl_init();
    
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 15,
        CURLOPT_CONNECTTIMEOUT => 10,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_MAXREDIRS => 3,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_USERAGENT => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        CURLOPT_HTTPHEADER => [
            'Accept: application/json, text/plain, */*',
            'Accept-Language: en-US,en;q=0.9',
            'Cache-Control: no-cache',
            'Connection: close'
        ]
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($response === false || !empty($error)) {
        return ['error' => 'cURL Error: ' . $error, 'http_code' => $httpCode, 'url' => $url];
    }
    
    if ($httpCode !== 200) {
        return ['error' => "HTTP Error: $httpCode", 'response' => substr($response, 0, 200), 'url' => $url];
    }
    
    $data = json_decode($response, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        return ['error' => 'JSON Error: ' . json_last_error_msg(), 'response' => substr($response, 0, 200)];
    }
    
    return $data;
}

// Verificar se o endpoint foi fornecido
if (!isset($_GET['endpoint'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Endpoint não especificado']);
    exit;
}

$endpoint = $_GET['endpoint'];

// Mapear endpoints
$endpointMap = [
    'summary' => 'summary',
    'summaryRaw' => 'summary',  // Usar summary em vez de summaryRaw
    'topClients' => 'topClients',
    'topBlocked' => 'topBlocked',
    'queryTypesOverTime' => 'queryTypesOverTime',
    'overTimeDataClients' => 'overTimeDataClients',
    'version' => 'version',
    'status' => 'status'
];

// Endpoint de teste especial
if ($endpoint === 'test') {
    $tests = [];
    
    // Teste básico
    $tests['basic'] = fetchPiholeDataCurl('status');
    
    // Teste com autenticação
    $tests['auth'] = fetchPiholeDataCurl('summary');
    
    echo json_encode([
        'pihole_ip' => PIHOLE_IP,
        'curl_available' => function_exists('curl_init'),
        'tests' => $tests,
        'timestamp' => time()
    ], JSON_PRETTY_PRINT);
    exit;
}

// Verificar se o endpoint é válido
if (!array_key_exists($endpoint, $endpointMap)) {
    http_response_code(400);
    echo json_encode(['error' => 'Endpoint inválido']);
    exit;
}

// Fazer a requisição
$piholeEndpoint = $endpointMap[$endpoint];
$params = [];

// Adicionar parâmetros específicos
switch ($endpoint) {
    case 'topClients':
    case 'topBlocked':
        $params['limit'] = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
        break;
    case 'overTimeDataClients':
        $params['from'] = time() - (24 * 60 * 60);
        $params['until'] = time();
        break;
}

$result = fetchPiholeDataCurl($piholeEndpoint, $params);

// Processar resultado
switch ($endpoint) {
    case 'summaryRaw':
    case 'summary':
        if (isset($result['dns_queries_today'])) {
            $result['dns_queries_today'] = (int)$result['dns_queries_today'];
        }
        if (isset($result['ads_blocked_today'])) {
            $result['ads_blocked_today'] = (int)$result['ads_blocked_today'];
        }
        if (isset($result['ads_percentage_today'])) {
            $result['ads_percentage_today'] = (float)$result['ads_percentage_today'];
        }
        if (isset($result['unique_domains'])) {
            $result['unique_domains'] = (int)$result['unique_domains'];
        }
        break;
        
    case 'topClients':
    case 'topBlocked':
        if (is_array($result)) {
            foreach ($result as $key => $value) {
                if (is_numeric($value)) {
                    $result[$key] = (int)$value;
                }
            }
        }
        break;
}

echo json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
?>
