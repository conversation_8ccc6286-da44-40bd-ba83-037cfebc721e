<?php
// Habilitar exibição de erros para debug
error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Configurações do Pi-hole
define('PIHOLE_IP', '************');
define('API_TOKEN', 'MAFHUcZbmK4ulb42wIXPUUd+hjTv1pTMgqduUfG7daI=');
define('PIHOLE_URL', 'http://' . PIHOLE_IP . '/admin/api.php');
define('DEBUG', true);

// Função para fazer requisições à API do Pi-hole
function fetchPiholeData($endpoint, $params = []) {
    $url = PIHOLE_URL . '?' . $endpoint;

    // Adicionar parâmetros se fornecidos
    if (!empty($params)) {
        $url .= '&' . http_build_query($params);
    }

    // Adicionar token para endpoints que precisam de autenticação
    $authEndpoints = ['topClients', 'topBlocked', 'queryTypesOverTime', 'overTimeDataClients', 'enable', 'disable'];
    if (in_array($endpoint, $authEndpoints)) {
        $url .= '&auth=' . urlencode(API_TOKEN);
    }

    if (DEBUG) {
        error_log("Tentando acessar URL: " . $url);
    }

    // Configurar contexto para a requisição
    $context = stream_context_create([
        'http' => [
            'timeout' => 15,
            'method' => 'GET',
            'header' => [
                'User-Agent: Pi-hole Dashboard/1.0',
                'Accept: application/json'
            ],
            'ignore_errors' => true
        ]
    ]);

    try {
        // Primeiro, vamos testar se conseguimos fazer ping no IP
        $ping_result = @fsockopen(PIHOLE_IP, 80, $errno, $errstr, 5);
        if (!$ping_result) {
            throw new Exception("Não foi possível conectar ao Pi-hole em " . PIHOLE_IP . ":80 - $errstr ($errno)");
        }
        fclose($ping_result);

        $response = @file_get_contents($url, false, $context);

        if ($response === false) {
            $error = error_get_last();
            throw new Exception('Falha ao conectar com o Pi-hole: ' . ($error['message'] ?? 'Erro desconhecido'));
        }

        if (DEBUG) {
            error_log("Resposta recebida: " . substr($response, 0, 200) . "...");
        }

        $data = json_decode($response, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception('Resposta inválida do Pi-hole: ' . json_last_error_msg() . '. Resposta: ' . substr($response, 0, 100));
        }

        return $data;

    } catch (Exception $e) {
        $errorMsg = 'Erro na API do Pi-hole: ' . $e->getMessage();
        if (DEBUG) {
            error_log($errorMsg);
        }
        return ['error' => $errorMsg, 'url_attempted' => $url];
    }
}

// Verificar se o endpoint foi fornecido
if (!isset($_GET['endpoint'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Endpoint não especificado']);
    exit;
}

$endpoint = $_GET['endpoint'];

// Mapear endpoints para as chamadas da API do Pi-hole
$endpointMap = [
    'summary' => 'summary',
    'summaryRaw' => 'summaryRaw',
    'topClients' => 'topClients',
    'topBlocked' => 'topBlocked',
    'queryTypesOverTime' => 'queryTypesOverTime',
    'overTimeDataClients' => 'overTimeDataClients',
    'version' => 'version',
    'status' => 'status',
    'enable' => 'enable',
    'disable' => 'disable'
];

// Verificar se o endpoint é válido
if (!array_key_exists($endpoint, $endpointMap)) {
    http_response_code(400);
    echo json_encode(['error' => 'Endpoint inválido']);
    exit;
}

// Fazer a requisição para o Pi-hole
$piholeEndpoint = $endpointMap[$endpoint];
$params = [];

// Adicionar parâmetros específicos para alguns endpoints
switch ($endpoint) {
    case 'topClients':
    case 'topBlocked':
        $params['limit'] = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
        break;
    case 'overTimeDataClients':
        // Buscar dados das últimas 24 horas
        $params['from'] = time() - (24 * 60 * 60);
        $params['until'] = time();
        break;
}

// Executar a requisição
$result = fetchPiholeData($piholeEndpoint, $params);

// Processar alguns endpoints para melhor formatação
switch ($endpoint) {
    case 'summaryRaw':
        // Garantir que os valores numéricos sejam números
        if (isset($result['dns_queries_today'])) {
            $result['dns_queries_today'] = (int)$result['dns_queries_today'];
        }
        if (isset($result['ads_blocked_today'])) {
            $result['ads_blocked_today'] = (int)$result['ads_blocked_today'];
        }
        if (isset($result['ads_percentage_today'])) {
            $result['ads_percentage_today'] = (float)$result['ads_percentage_today'];
        }
        if (isset($result['unique_domains'])) {
            $result['unique_domains'] = (int)$result['unique_domains'];
        }
        break;
        
    case 'topClients':
    case 'topBlocked':
        // Garantir que os valores sejam números
        if (is_array($result)) {
            foreach ($result as $key => $value) {
                if (is_numeric($value)) {
                    $result[$key] = (int)$value;
                }
            }
        }
        break;
        
    case 'version':
        // Formatar informações de versão
        if (isset($result['version'])) {
            $result['version'] = $result['version'];
        }
        break;
}

// Retornar o resultado
echo json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);

// Função para log de debug (opcional)
function debugLog($message) {
    if (defined('DEBUG') && DEBUG) {
        error_log('[Pi-hole Dashboard] ' . $message);
    }
}

// Função para validar IP
function isValidIP($ip) {
    return filter_var($ip, FILTER_VALIDATE_IP) !== false;
}

// Função para sanitizar entrada
function sanitizeInput($input) {
    return htmlspecialchars(strip_tags(trim($input)), ENT_QUOTES, 'UTF-8');
}

// Verificar se o Pi-hole está acessível (função de teste)
function testPiholeConnection() {
    $url = PIHOLE_URL . '?status';
    $context = stream_context_create([
        'http' => [
            'timeout' => 5,
            'method' => 'GET'
        ]
    ]);
    
    $response = @file_get_contents($url, false, $context);
    return $response !== false;
}

// Endpoint especial para testar conexão
if ($endpoint === 'test') {
    $testResults = [];

    // Teste 1: Ping básico
    $ping_result = @fsockopen(PIHOLE_IP, 80, $errno, $errstr, 5);
    $testResults['ping_80'] = $ping_result !== false;
    if ($ping_result) fclose($ping_result);

    // Teste 2: Conexão HTTP básica
    $basic_url = 'http://' . PIHOLE_IP . '/admin/api.php?status';
    $basic_response = @file_get_contents($basic_url, false, stream_context_create([
        'http' => ['timeout' => 10]
    ]));
    $testResults['http_basic'] = $basic_response !== false;
    $testResults['basic_response'] = $basic_response ? substr($basic_response, 0, 100) : 'Falha';

    // Teste 3: Teste com token
    $auth_url = 'http://' . PIHOLE_IP . '/admin/api.php?summaryRaw&auth=' . urlencode(API_TOKEN);
    $auth_response = @file_get_contents($auth_url, false, stream_context_create([
        'http' => ['timeout' => 10]
    ]));
    $testResults['http_with_auth'] = $auth_response !== false;
    $testResults['auth_response'] = $auth_response ? substr($auth_response, 0, 100) : 'Falha';

    echo json_encode([
        'pihole_ip' => PIHOLE_IP,
        'api_token_length' => strlen(API_TOKEN),
        'timestamp' => time(),
        'tests' => $testResults,
        'php_version' => PHP_VERSION,
        'curl_available' => function_exists('curl_init')
    ], JSON_PRETTY_PRINT);
    exit;
}

// Endpoint para debug completo
if ($endpoint === 'debug') {
    echo json_encode([
        'pihole_ip' => PIHOLE_IP,
        'pihole_url' => PIHOLE_URL,
        'api_token' => substr(API_TOKEN, 0, 10) . '...',
        'php_version' => PHP_VERSION,
        'extensions' => get_loaded_extensions(),
        'ini_settings' => [
            'allow_url_fopen' => ini_get('allow_url_fopen'),
            'default_socket_timeout' => ini_get('default_socket_timeout'),
            'user_agent' => ini_get('user_agent')
        ]
    ], JSON_PRETTY_PRINT);
    exit;
}
?>
