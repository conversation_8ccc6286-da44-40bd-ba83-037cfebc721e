<?php
// API Proxy para Pi-hole - Baseado na documentação oficial
error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Configurações do Pi-hole
define('PIHOLE_IP', '************');
define('API_TOKEN', 'MAFHUcZbmK4ulb42wIXPUUd+hjTv1pTMgqduUfG7daI=');
define('PIHOLE_BASE_URL', 'http://' . PIHOLE_IP . '/api');
define('DEBUG', true);

// Função para fazer requisições à API do Pi-hole usando cURL
function fetchPiholeData($endpoint, $method = 'GET', $data = null) {
    // Mapear endpoints para URLs da nova API
    $endpointMap = [
        'status' => '/dns/blocking',
        'summary' => '/stats/summary',
        'summaryRaw' => '/stats/summary',
        'topClients' => '/stats/top_clients',
        'topBlocked' => '/stats/top_blocked',
        'queryTypesOverTime' => '/stats/query_types_over_time',
        'overTimeDataClients' => '/stats/over_time_data_clients',
        'version' => '/versions',
        'enable' => '/dns/blocking',
        'disable' => '/dns/blocking'
    ];

    // Verificar se o endpoint existe
    if (!isset($endpointMap[$endpoint])) {
        return ['error' => "Endpoint '$endpoint' não encontrado"];
    }

    $url = PIHOLE_BASE_URL . $endpointMap[$endpoint];

    if (DEBUG) {
        error_log("Fazendo requisição $method para: $url");
    }

    $ch = curl_init();

    // Configurações básicas do cURL
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 15,
        CURLOPT_CONNECTTIMEOUT => 10,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_MAXREDIRS => 3,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_USERAGENT => 'Pi-hole Dashboard/1.0',
        CURLOPT_HTTPHEADER => [
            'Accept: application/json',
            'Content-Type: application/json',
            'X-Pi-hole-Authenticate: ' . API_TOKEN
        ]
    ]);

    // Configurar método HTTP
    switch (strtoupper($method)) {
        case 'POST':
            curl_setopt($ch, CURLOPT_POST, true);
            if ($data) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            }
            break;
        case 'PUT':
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
            if ($data) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            }
            break;
        case 'PATCH':
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PATCH');
            if ($data) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            }
            break;
        case 'DELETE':
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');
            break;
    }

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);

    if ($response === false || !empty($error)) {
        return ['error' => 'cURL Error: ' . $error, 'http_code' => $httpCode];
    }

    if ($httpCode >= 400) {
        return ['error' => "HTTP Error: $httpCode", 'response' => $response, 'http_code' => $httpCode];
    }

    $data = json_decode($response, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        return ['error' => 'JSON Error: ' . json_last_error_msg(), 'response' => substr($response, 0, 200)];
    }

    if (DEBUG) {
        error_log("Resposta bem-sucedida: " . substr($response, 0, 200));
    }

    return $data;
}

// Verificar se o endpoint foi fornecido
if (!isset($_GET['endpoint'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Endpoint não especificado']);
    exit;
}

$endpoint = $_GET['endpoint'];

// Endpoints especiais para teste
if ($endpoint === 'test') {
    $tests = [];

    // Teste de conectividade básica
    $tests['connectivity'] = fetchPiholeData('status');

    // Teste de dados estatísticos
    $tests['stats'] = fetchPiholeData('summary');

    echo json_encode([
        'pihole_ip' => PIHOLE_IP,
        'api_base_url' => PIHOLE_BASE_URL,
        'curl_available' => function_exists('curl_init'),
        'tests' => $tests,
        'timestamp' => time()
    ], JSON_PRETTY_PRINT);
    exit;
}

// Verificar se cURL está disponível
if (!function_exists('curl_init')) {
    http_response_code(500);
    echo json_encode(['error' => 'cURL não está disponível no servidor']);
    exit;
}

// Executar a requisição
$method = $_SERVER['REQUEST_METHOD'] ?? 'GET';
$data = null;

// Para requisições POST/PUT/PATCH, obter dados do corpo
if (in_array($method, ['POST', 'PUT', 'PATCH'])) {
    $input = file_get_contents('php://input');
    if (!empty($input)) {
        $data = json_decode($input, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            http_response_code(400);
            echo json_encode(['error' => 'JSON inválido no corpo da requisição']);
            exit;
        }
    }
}

$result = fetchPiholeData($endpoint, $method, $data);

// Verificar se houve erro na requisição
if (isset($result['error'])) {
    // Determinar código de status HTTP baseado no erro
    $httpCode = 500; // Padrão para erro interno

    if (isset($result['http_code'])) {
        $httpCode = $result['http_code'];
    } elseif (strpos($result['error'], 'cURL Error') !== false) {
        $httpCode = 503; // Service Unavailable
    } elseif (strpos($result['error'], 'JSON Error') !== false) {
        $httpCode = 502; // Bad Gateway
    }

    http_response_code($httpCode);
}

// Processar alguns endpoints para melhor compatibilidade com a dashboard
switch ($endpoint) {
    case 'summary':
    case 'summaryRaw':
        // Garantir que os valores numéricos sejam números
        if (isset($result['dns_queries_today'])) {
            $result['dns_queries_today'] = (int)$result['dns_queries_today'];
        }
        if (isset($result['ads_blocked_today'])) {
            $result['ads_blocked_today'] = (int)$result['ads_blocked_today'];
        }
        if (isset($result['ads_percentage_today'])) {
            $result['ads_percentage_today'] = (float)$result['ads_percentage_today'];
        }
        if (isset($result['unique_domains'])) {
            $result['unique_domains'] = (int)$result['unique_domains'];
        }
        break;

    case 'status':
        // Converter resposta de blocking para formato esperado
        if (isset($result['blocking'])) {
            $result['status'] = $result['blocking'] ? 'enabled' : 'disabled';
        }
        break;
}

// Retornar o resultado
echo json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);

// Função para log de debug
function debugLog($message) {
    if (defined('DEBUG') && DEBUG) {
        error_log('[Pi-hole Dashboard] ' . $message);
    }
}

// Endpoint para debug completo
if ($endpoint === 'debug') {
    echo json_encode([
        'pihole_ip' => PIHOLE_IP,
        'api_base_url' => PIHOLE_BASE_URL,
        'api_token' => substr(API_TOKEN, 0, 10) . '...',
        'php_version' => PHP_VERSION,
        'curl_available' => function_exists('curl_init'),
        'curl_version' => function_exists('curl_version') ? curl_version() : 'N/A'
    ], JSON_PRETTY_PRINT);
    exit;
}
?>
