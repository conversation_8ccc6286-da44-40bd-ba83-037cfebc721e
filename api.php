<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Configurações do Pi-hole
define('PIHOLE_IP', '************');
define('API_TOKEN', 'MAFHUcZbmK4ulb42wIXPUUd+hjTv1pTMgqduUfG7daI=');
define('PIHOLE_URL', 'http://' . PIHOLE_IP . '/admin/api.php');

// Função para fazer requisições à API do Pi-hole
function fetchPiholeData($endpoint, $params = []) {
    $url = PIHOLE_URL . '?' . $endpoint;
    
    // Adicionar token de autenticação se necessário
    if (!empty($params)) {
        $url .= '&' . http_build_query($params);
    }
    
    // Adicionar token para endpoints que precisam de autenticação
    $authEndpoints = ['topClients', 'topBlocked', 'queryTypesOverTime', 'overTimeDataClients', 'enable', 'disable'];
    if (in_array($endpoint, $authEndpoints)) {
        $url .= '&auth=' . API_TOKEN;
    }
    
    // Configurar contexto para a requisição
    $context = stream_context_create([
        'http' => [
            'timeout' => 10,
            'method' => 'GET',
            'header' => [
                'User-Agent: Pi-hole Dashboard/1.0'
            ]
        ]
    ]);
    
    try {
        $response = file_get_contents($url, false, $context);
        
        if ($response === false) {
            throw new Exception('Falha ao conectar com o Pi-hole');
        }
        
        $data = json_decode($response, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception('Resposta inválida do Pi-hole: ' . json_last_error_msg());
        }
        
        return $data;
        
    } catch (Exception $e) {
        error_log('Erro na API do Pi-hole: ' . $e->getMessage());
        return ['error' => $e->getMessage()];
    }
}

// Verificar se o endpoint foi fornecido
if (!isset($_GET['endpoint'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Endpoint não especificado']);
    exit;
}

$endpoint = $_GET['endpoint'];

// Mapear endpoints para as chamadas da API do Pi-hole
$endpointMap = [
    'summary' => 'summary',
    'summaryRaw' => 'summaryRaw',
    'topClients' => 'topClients',
    'topBlocked' => 'topBlocked',
    'queryTypesOverTime' => 'queryTypesOverTime',
    'overTimeDataClients' => 'overTimeDataClients',
    'version' => 'version',
    'status' => 'status',
    'enable' => 'enable',
    'disable' => 'disable'
];

// Verificar se o endpoint é válido
if (!array_key_exists($endpoint, $endpointMap)) {
    http_response_code(400);
    echo json_encode(['error' => 'Endpoint inválido']);
    exit;
}

// Fazer a requisição para o Pi-hole
$piholeEndpoint = $endpointMap[$endpoint];
$params = [];

// Adicionar parâmetros específicos para alguns endpoints
switch ($endpoint) {
    case 'topClients':
    case 'topBlocked':
        $params['limit'] = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
        break;
    case 'overTimeDataClients':
        // Buscar dados das últimas 24 horas
        $params['from'] = time() - (24 * 60 * 60);
        $params['until'] = time();
        break;
}

// Executar a requisição
$result = fetchPiholeData($piholeEndpoint, $params);

// Processar alguns endpoints para melhor formatação
switch ($endpoint) {
    case 'summaryRaw':
        // Garantir que os valores numéricos sejam números
        if (isset($result['dns_queries_today'])) {
            $result['dns_queries_today'] = (int)$result['dns_queries_today'];
        }
        if (isset($result['ads_blocked_today'])) {
            $result['ads_blocked_today'] = (int)$result['ads_blocked_today'];
        }
        if (isset($result['ads_percentage_today'])) {
            $result['ads_percentage_today'] = (float)$result['ads_percentage_today'];
        }
        if (isset($result['unique_domains'])) {
            $result['unique_domains'] = (int)$result['unique_domains'];
        }
        break;
        
    case 'topClients':
    case 'topBlocked':
        // Garantir que os valores sejam números
        if (is_array($result)) {
            foreach ($result as $key => $value) {
                if (is_numeric($value)) {
                    $result[$key] = (int)$value;
                }
            }
        }
        break;
        
    case 'version':
        // Formatar informações de versão
        if (isset($result['version'])) {
            $result['version'] = $result['version'];
        }
        break;
}

// Retornar o resultado
echo json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);

// Função para log de debug (opcional)
function debugLog($message) {
    if (defined('DEBUG') && DEBUG) {
        error_log('[Pi-hole Dashboard] ' . $message);
    }
}

// Função para validar IP
function isValidIP($ip) {
    return filter_var($ip, FILTER_VALIDATE_IP) !== false;
}

// Função para sanitizar entrada
function sanitizeInput($input) {
    return htmlspecialchars(strip_tags(trim($input)), ENT_QUOTES, 'UTF-8');
}

// Verificar se o Pi-hole está acessível (função de teste)
function testPiholeConnection() {
    $url = PIHOLE_URL . '?status';
    $context = stream_context_create([
        'http' => [
            'timeout' => 5,
            'method' => 'GET'
        ]
    ]);
    
    $response = @file_get_contents($url, false, $context);
    return $response !== false;
}

// Endpoint especial para testar conexão
if ($endpoint === 'test') {
    $isConnected = testPiholeConnection();
    echo json_encode([
        'connected' => $isConnected,
        'pihole_ip' => PIHOLE_IP,
        'timestamp' => time()
    ]);
    exit;
}
?>
