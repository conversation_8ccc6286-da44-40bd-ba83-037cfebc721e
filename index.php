<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Pi-hole</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header>
            <h1><i class="fas fa-shield-alt"></i> Dashboard Pi-hole</h1>
            <div class="status-indicator" id="statusIndicator">
                <span class="status-dot"></span>
                <span class="status-text">Verificando...</span>
            </div>
        </header>

        <div class="dashboard-grid">
            <!-- Estatísticas Principais -->
            <div class="card stats-card">
                <h2><i class="fas fa-chart-bar"></i> Estatísticas Hoje</h2>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value" id="totalQueries">-</div>
                        <div class="stat-label">Total de Consultas</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="blockedQueries">-</div>
                        <div class="stat-label">Consultas Bloqueadas</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="percentBlocked">-</div>
                        <div class="stat-label">% Bloqueadas</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="uniqueDomains">-</div>
                        <div class="stat-label">Domínios Únicos</div>
                    </div>
                </div>
            </div>

            <!-- Status do Sistema -->
            <div class="card">
                <h2><i class="fas fa-server"></i> Status do Sistema</h2>
                <div class="system-info">
                    <div class="info-row">
                        <span class="info-label">Status:</span>
                        <span class="info-value" id="systemStatus">-</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Versão:</span>
                        <span class="info-value" id="version">-</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Uptime:</span>
                        <span class="info-value" id="uptime">-</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Temperatura:</span>
                        <span class="info-value" id="temperature">-</span>
                    </div>
                </div>
            </div>

            <!-- Top Domínios Bloqueados -->
            <div class="card">
                <h2><i class="fas fa-ban"></i> Top Domínios Bloqueados</h2>
                <div class="list-container" id="topBlockedDomains">
                    <div class="loading">Carregando...</div>
                </div>
            </div>

            <!-- Top Clientes -->
            <div class="card">
                <h2><i class="fas fa-users"></i> Top Clientes</h2>
                <div class="list-container" id="topClients">
                    <div class="loading">Carregando...</div>
                </div>
            </div>

            <!-- Consultas ao Longo do Tempo -->
            <div class="card chart-card">
                <h2><i class="fas fa-chart-line"></i> Consultas ao Longo do Tempo</h2>
                <div class="chart-container">
                    <canvas id="queriesChart"></canvas>
                </div>
            </div>

            <!-- Tipos de Consulta -->
            <div class="card">
                <h2><i class="fas fa-pie-chart"></i> Tipos de Consulta</h2>
                <div class="query-types" id="queryTypes">
                    <div class="loading">Carregando...</div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Configuração
        const PIHOLE_IP = '************';
        const API_TOKEN = 'MAFHUcZbmK4ulb42wIXPUUd+hjTv1pTMgqduUfG7daI=';
        
        // Variáveis globais
        let queriesChart;
        
        // Função para fazer requisições à API
        async function fetchPiholeData(endpoint) {
            try {
                // Usar a nova API com autenticação por sessão
                const response = await fetch(`api.php?endpoint=${endpoint}`);
                const data = await response.json();

                if (data.error) {
                    console.error('Erro na API:', data.error);
                    return null;
                }

                return data;
            } catch (error) {
                console.error('Erro ao buscar dados:', error);
                return null;
            }
        }
        
        // Atualizar estatísticas principais
        async function updateStats() {
            const data = await fetchPiholeData('summaryRaw');
            if (data) {
                document.getElementById('totalQueries').textContent = data.dns_queries_today?.toLocaleString() || '-';
                document.getElementById('blockedQueries').textContent = data.ads_blocked_today?.toLocaleString() || '-';
                document.getElementById('percentBlocked').textContent = data.ads_percentage_today ? data.ads_percentage_today.toFixed(1) + '%' : '-';
                document.getElementById('uniqueDomains').textContent = data.unique_domains?.toLocaleString() || '-';
                
                // Atualizar indicador de status
                const statusIndicator = document.getElementById('statusIndicator');
                const statusDot = statusIndicator.querySelector('.status-dot');
                const statusText = statusIndicator.querySelector('.status-text');
                
                if (data.status === 'enabled') {
                    statusDot.className = 'status-dot active';
                    statusText.textContent = 'Ativo';
                } else {
                    statusDot.className = 'status-dot inactive';
                    statusText.textContent = 'Inativo';
                }
            }
        }
        
        // Atualizar informações do sistema
        async function updateSystemInfo() {
            const data = await fetchPiholeData('version');
            if (data) {
                document.getElementById('systemStatus').textContent = 'Online';
                document.getElementById('version').textContent = data.version || '-';
            }
            
            // Simular uptime e temperatura (Pi-hole não fornece essas informações diretamente)
            document.getElementById('uptime').textContent = 'N/A';
            document.getElementById('temperature').textContent = 'N/A';
        }
        
        // Atualizar top domínios bloqueados
        async function updateTopBlockedDomains() {
            const data = await fetchPiholeData('topBlocked');
            const container = document.getElementById('topBlockedDomains');
            
            if (data && typeof data === 'object') {
                let html = '';
                let count = 0;
                for (const [domain, hits] of Object.entries(data)) {
                    if (count >= 10) break;
                    html += `
                        <div class="list-item">
                            <span class="domain">${domain}</span>
                            <span class="count">${hits.toLocaleString()}</span>
                        </div>
                    `;
                    count++;
                }
                container.innerHTML = html || '<div class="no-data">Nenhum dado disponível</div>';
            } else {
                container.innerHTML = '<div class="no-data">Nenhum dado disponível</div>';
            }
        }
        
        // Atualizar top clientes
        async function updateTopClients() {
            const data = await fetchPiholeData('topClients');
            const container = document.getElementById('topClients');
            
            if (data && typeof data === 'object') {
                let html = '';
                let count = 0;
                for (const [client, queries] of Object.entries(data)) {
                    if (count >= 10) break;
                    html += `
                        <div class="list-item">
                            <span class="domain">${client}</span>
                            <span class="count">${queries.toLocaleString()}</span>
                        </div>
                    `;
                    count++;
                }
                container.innerHTML = html || '<div class="no-data">Nenhum dado disponível</div>';
            } else {
                container.innerHTML = '<div class="no-data">Nenhum dado disponível</div>';
            }
        }
        
        // Atualizar tipos de consulta
        async function updateQueryTypes() {
            const data = await fetchPiholeData('queryTypesOverTime');
            const container = document.getElementById('queryTypes');
            
            if (data && typeof data === 'object') {
                let html = '';
                for (const [type, count] of Object.entries(data)) {
                    if (type !== 'over_time') {
                        html += `
                            <div class="query-type-item">
                                <span class="type-label">${type}</span>
                                <span class="type-count">${count.toLocaleString()}</span>
                            </div>
                        `;
                    }
                }
                container.innerHTML = html || '<div class="no-data">Nenhum dado disponível</div>';
            } else {
                container.innerHTML = '<div class="no-data">Nenhum dado disponível</div>';
            }
        }
        
        // Criar gráfico de consultas
        async function createQueriesChart() {
            const data = await fetchPiholeData('overTimeDataClients');
            
            if (data && data.over_time) {
                const ctx = document.getElementById('queriesChart').getContext('2d');
                
                const labels = Object.keys(data.over_time).map(timestamp => {
                    const date = new Date(parseInt(timestamp) * 1000);
                    return date.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' });
                });
                
                const values = Object.values(data.over_time);
                
                if (queriesChart) {
                    queriesChart.destroy();
                }
                
                queriesChart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: labels,
                        datasets: [{
                            label: 'Consultas',
                            data: values,
                            borderColor: '#4CAF50',
                            backgroundColor: 'rgba(76, 175, 80, 0.1)',
                            tension: 0.4,
                            fill: true
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        },
                        plugins: {
                            legend: {
                                display: false
                            }
                        }
                    }
                });
            }
        }
        
        // Função principal para atualizar todos os dados
        async function updateDashboard() {
            await Promise.all([
                updateStats(),
                updateSystemInfo(),
                updateTopBlockedDomains(),
                updateTopClients(),
                updateQueryTypes(),
                createQueriesChart()
            ]);
        }
        
        // Inicializar dashboard
        document.addEventListener('DOMContentLoaded', function() {
            updateDashboard();
            
            // Atualizar a cada 30 segundos
            setInterval(updateDashboard, 30000);
        });
    </script>
</body>
</html>
