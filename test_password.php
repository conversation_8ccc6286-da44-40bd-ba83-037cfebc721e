<?php
// Teste de diferentes formatos de senha
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Teste de Senha Pi-hole</h1>";

$pihole_ip = '************';
$auth_url = "http://$pihole_ip/api/auth";

// Diferentes formatos de senha para testar
$passwords = [
    'uVaP7zc5',                    // Senha simples
    hash('sha256', 'uVaP7zc5'),    // SHA256 da senha
    hash('sha1', 'uVaP7zc5'),      // SHA1 da senha
    md5('uVaP7zc5'),               // MD5 da senha
];

echo "<h2>Testando diferentes formatos de senha:</h2>";

foreach ($passwords as $index => $password) {
    echo "<h3>Teste " . ($index + 1) . ":</h3>";
    echo "Senha: " . htmlspecialchars($password) . "<br>";
    echo "Tipo: ";
    
    switch ($index) {
        case 0: echo "Senha original"; break;
        case 1: echo "SHA256"; break;
        case 2: echo "SHA1"; break;
        case 3: echo "MD5"; break;
    }
    echo "<br>";
    
    $loginData = ['password' => $password];
    
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $auth_url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => json_encode($loginData),
        CURLOPT_TIMEOUT => 10,
        CURLOPT_HTTPHEADER => [
            'Content-Type: application/json',
            'Accept: application/json'
        ]
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    echo "Status HTTP: $httpCode<br>";
    
    if ($response === false || !empty($error)) {
        echo "❌ Erro cURL: " . htmlspecialchars($error) . "<br>";
    } else {
        echo "Resposta: " . htmlspecialchars($response) . "<br>";
        
        $result = json_decode($response, true);
        if ($result && isset($result['session'])) {
            if ($result['session']['valid']) {
                echo "✅ <strong>SUCESSO! Esta senha funciona!</strong><br>";
                echo "Session ID: " . htmlspecialchars(substr($result['session']['sid'], 0, 20)) . "...<br>";
                break; // Parar no primeiro sucesso
            } else {
                echo "❌ Senha incorreta: " . htmlspecialchars($result['session']['message']) . "<br>";
            }
        }
    }
    echo "<hr>";
}

echo "<h2>Informações Adicionais:</h2>";
echo "Se nenhuma senha funcionou, verifique:<br>";
echo "1. Acesse http://************/admin e confirme a senha<br>";
echo "2. Verifique se o Pi-hole está na versão 6+<br>";
echo "3. Verifique se a API está habilitada<br>";
echo "4. Tente redefinir a senha do Pi-hole<br>";

// Teste adicional: verificar se conseguimos acessar a interface web
echo "<h3>Teste de Conectividade Web:</h3>";
$web_url = "http://$pihole_ip/admin";
echo "Testando acesso a: $web_url<br>";

$ch_web = curl_init();
curl_setopt_array($ch_web, [
    CURLOPT_URL => $web_url,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_TIMEOUT => 10,
    CURLOPT_FOLLOWLOCATION => true
]);

$web_response = curl_exec($ch_web);
$web_code = curl_getinfo($ch_web, CURLINFO_HTTP_CODE);
curl_close($ch_web);

echo "Status HTTP: $web_code<br>";
if ($web_code == 200) {
    echo "✅ Interface web acessível<br>";
} else {
    echo "❌ Problema ao acessar interface web<br>";
}
?>
