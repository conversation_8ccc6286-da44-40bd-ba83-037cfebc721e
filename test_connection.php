<?php
// Teste simples de conexão com o Pi-hole
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Teste de Conexão Pi-hole</h1>";

$pihole_ip = '************';
$api_token = 'MAFHUcZbmK4ulb42wIXPUUd+hjTv1pTMgqduUfG7daI=';

echo "<h2>Informações do Sistema:</h2>";
echo "PHP Version: " . PHP_VERSION . "<br>";
echo "allow_url_fopen: " . (ini_get('allow_url_fopen') ? 'Habilitado' : 'Desabilitado') . "<br>";
echo "cURL disponível: " . (function_exists('curl_init') ? 'Sim' : 'Não') . "<br>";

echo "<h2>Teste 1: Ping básico (porta 80)</h2>";
$ping_result = @fsockopen($pihole_ip, 80, $errno, $errstr, 5);
if ($ping_result) {
    echo "✅ Conexão TCP para $pihole_ip:80 bem-sucedida<br>";
    fclose($ping_result);
} else {
    echo "❌ Falha na conexão TCP para $pihole_ip:80 - $errstr ($errno)<br>";
}

echo "<h2>Teste 2: API sem autenticação</h2>";
$url_basic = "http://$pihole_ip/admin/api.php?status";
echo "URL: $url_basic<br>";

$context = stream_context_create([
    'http' => [
        'timeout' => 10,
        'method' => 'GET'
    ]
]);

$response_basic = @file_get_contents($url_basic, false, $context);
if ($response_basic !== false) {
    echo "✅ Resposta recebida: " . htmlspecialchars($response_basic) . "<br>";
} else {
    $error = error_get_last();
    echo "❌ Falha na requisição: " . htmlspecialchars($error['message'] ?? 'Erro desconhecido') . "<br>";
}

echo "<h2>Teste 3: API com autenticação - Tentativa 1 (token sem encoding)</h2>";
$url_auth1 = "http://$pihole_ip/admin/api.php?summaryRaw&auth=" . $api_token;
echo "URL: " . htmlspecialchars(str_replace($api_token, '[TOKEN_OCULTO]', $url_auth1)) . "<br>";

$context_better = stream_context_create([
    'http' => [
        'timeout' => 10,
        'method' => 'GET',
        'header' => [
            'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept: application/json, text/plain, */*',
            'Accept-Language: en-US,en;q=0.9',
            'Cache-Control: no-cache',
            'Connection: close'
        ]
    ]
]);

$response_auth1 = @file_get_contents($url_auth1, false, $context_better);
if ($response_auth1 !== false) {
    echo "✅ Resposta recebida: " . htmlspecialchars(substr($response_auth1, 0, 200)) . "...<br>";

    $data = json_decode($response_auth1, true);
    if ($data && json_last_error() === JSON_ERROR_NONE) {
        echo "✅ JSON válido decodificado<br>";
        echo "<pre>" . htmlspecialchars(json_encode($data, JSON_PRETTY_PRINT)) . "</pre>";
    } else {
        echo "❌ Erro ao decodificar JSON: " . json_last_error_msg() . "<br>";
    }
} else {
    $error = error_get_last();
    echo "❌ Falha na requisição: " . htmlspecialchars($error['message'] ?? 'Erro desconhecido') . "<br>";
}

echo "<h2>Teste 4: API com autenticação - Tentativa 2 (token com URL encoding)</h2>";
$url_auth2 = "http://$pihole_ip/admin/api.php?summaryRaw&auth=" . urlencode($api_token);
echo "URL: " . htmlspecialchars(str_replace(urlencode($api_token), '[TOKEN_OCULTO]', $url_auth2)) . "<br>";

$response_auth2 = @file_get_contents($url_auth2, false, $context_better);
if ($response_auth2 !== false) {
    echo "✅ Resposta recebida: " . htmlspecialchars(substr($response_auth2, 0, 200)) . "...<br>";
} else {
    $error = error_get_last();
    echo "❌ Falha na requisição: " . htmlspecialchars($error['message'] ?? 'Erro desconhecido') . "<br>";
}

echo "<h2>Teste 5: Tentativa com summary (sem Raw)</h2>";
$url_summary = "http://$pihole_ip/admin/api.php?summary&auth=" . $api_token;
$response_summary = @file_get_contents($url_summary, false, $context_better);
if ($response_summary !== false) {
    echo "✅ Summary funcionou: " . htmlspecialchars(substr($response_summary, 0, 200)) . "...<br>";
} else {
    $error = error_get_last();
    echo "❌ Summary falhou: " . htmlspecialchars($error['message'] ?? 'Erro desconhecido') . "<br>";
}

echo "<h2>Teste 6: Teste com cURL (se disponível)</h2>";
if (function_exists('curl_init')) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url_basic);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
    
    $curl_response = curl_exec($ch);
    $curl_error = curl_error($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($curl_response !== false && empty($curl_error)) {
        echo "✅ cURL bem-sucedido (HTTP $http_code): " . htmlspecialchars($curl_response) . "<br>";
    } else {
        echo "❌ Erro cURL: " . htmlspecialchars($curl_error) . "<br>";
    }
} else {
    echo "cURL não está disponível<br>";
}

echo "<h2>Teste 7: Verificar endpoints disponíveis</h2>";
$endpoints_to_test = ['summary', 'version', 'type'];
foreach ($endpoints_to_test as $test_endpoint) {
    $test_url = "http://$pihole_ip/admin/api.php?$test_endpoint";
    $test_response = @file_get_contents($test_url, false, $context_better);
    if ($test_response !== false) {
        echo "✅ $test_endpoint: " . htmlspecialchars(substr($test_response, 0, 100)) . "...<br>";
    } else {
        echo "❌ $test_endpoint: Falhou<br>";
    }
}

echo "<h2>Informações de Rede:</h2>";
echo "IP do servidor: " . ($_SERVER['SERVER_ADDR'] ?? 'N/A') . "<br>";
echo "Porta do servidor: " . ($_SERVER['SERVER_PORT'] ?? 'N/A') . "<br>";
echo "User Agent: " . ($_SERVER['HTTP_USER_AGENT'] ?? 'N/A') . "<br>";

echo "<h2>Diagnóstico do Token:</h2>";
echo "Comprimento do token: " . strlen($api_token) . " caracteres<br>";
echo "Primeiros 10 caracteres: " . htmlspecialchars(substr($api_token, 0, 10)) . "...<br>";
echo "Últimos 10 caracteres: ..." . htmlspecialchars(substr($api_token, -10)) . "<br>";
echo "Token contém caracteres especiais: " . (preg_match('/[^a-zA-Z0-9]/', $api_token) ? 'Sim' : 'Não') . "<br>";
?>
