<?php
// Teste simples de conexão com o Pi-hole
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Teste de Conexão Pi-hole</h1>";

$pihole_ip = '************';
$api_token = 'MAFHUcZbmK4ulb42wIXPUUd+hjTv1pTMgqduUfG7daI=';

echo "<h2>Informações do Sistema:</h2>";
echo "PHP Version: " . PHP_VERSION . "<br>";
echo "allow_url_fopen: " . (ini_get('allow_url_fopen') ? 'Habilitado' : 'Desabilitado') . "<br>";
echo "cURL disponível: " . (function_exists('curl_init') ? 'Sim' : 'Não') . "<br>";

echo "<h2>Teste 1: Ping básico (porta 80)</h2>";
$ping_result = @fsockopen($pihole_ip, 80, $errno, $errstr, 5);
if ($ping_result) {
    echo "✅ Conexão TCP para $pihole_ip:80 bem-sucedida<br>";
    fclose($ping_result);
} else {
    echo "❌ Falha na conexão TCP para $pihole_ip:80 - $errstr ($errno)<br>";
}

echo "<h2>Teste 2: API sem autenticação</h2>";
$url_basic = "http://$pihole_ip/admin/api.php?status";
echo "URL: $url_basic<br>";

$context = stream_context_create([
    'http' => [
        'timeout' => 10,
        'method' => 'GET'
    ]
]);

$response_basic = @file_get_contents($url_basic, false, $context);
if ($response_basic !== false) {
    echo "✅ Resposta recebida: " . htmlspecialchars($response_basic) . "<br>";
} else {
    $error = error_get_last();
    echo "❌ Falha na requisição: " . htmlspecialchars($error['message'] ?? 'Erro desconhecido') . "<br>";
}

echo "<h2>Teste 3: API com autenticação (summaryRaw)</h2>";
$url_auth = "http://$pihole_ip/admin/api.php?summaryRaw&auth=" . urlencode($api_token);
echo "URL: " . htmlspecialchars(str_replace($api_token, '[TOKEN_OCULTO]', $url_auth)) . "<br>";

$response_auth = @file_get_contents($url_auth, false, $context);
if ($response_auth !== false) {
    echo "✅ Resposta recebida: " . htmlspecialchars(substr($response_auth, 0, 200)) . "...<br>";
    
    $data = json_decode($response_auth, true);
    if ($data && json_last_error() === JSON_ERROR_NONE) {
        echo "✅ JSON válido decodificado<br>";
        echo "<pre>" . htmlspecialchars(json_encode($data, JSON_PRETTY_PRINT)) . "</pre>";
    } else {
        echo "❌ Erro ao decodificar JSON: " . json_last_error_msg() . "<br>";
    }
} else {
    $error = error_get_last();
    echo "❌ Falha na requisição autenticada: " . htmlspecialchars($error['message'] ?? 'Erro desconhecido') . "<br>";
}

echo "<h2>Teste 4: Teste com cURL (se disponível)</h2>";
if (function_exists('curl_init')) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url_basic);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
    
    $curl_response = curl_exec($ch);
    $curl_error = curl_error($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($curl_response !== false && empty($curl_error)) {
        echo "✅ cURL bem-sucedido (HTTP $http_code): " . htmlspecialchars($curl_response) . "<br>";
    } else {
        echo "❌ Erro cURL: " . htmlspecialchars($curl_error) . "<br>";
    }
} else {
    echo "cURL não está disponível<br>";
}

echo "<h2>Informações de Rede:</h2>";
echo "IP do servidor: " . $_SERVER['SERVER_ADDR'] ?? 'N/A' . "<br>";
echo "Porta do servidor: " . $_SERVER['SERVER_PORT'] ?? 'N/A' . "<br>";
echo "User Agent: " . $_SERVER['HTTP_USER_AGENT'] ?? 'N/A' . "<br>";
?>
