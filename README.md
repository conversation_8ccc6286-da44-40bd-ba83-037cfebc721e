# Dashboard Pi-hole

Uma dashboard moderna e responsiva para monitorar seu Pi-hole em tempo real.

## Características

- **Interface moderna**: Design responsivo com gradientes e efeitos visuais
- **Estatísticas em tempo real**: Consultas totais, bloqueadas, percentual e domínios únicos
- **Gráficos interativos**: Visualização das consultas ao longo do tempo
- **Top listas**: Domínios mais bloqueados e clientes mais ativos
- **Status do sistema**: Informações sobre versão e status do Pi-hole
- **Atualização automática**: Dados atualizados a cada 30 segundos

## Pré-requisitos

- Servidor web com PHP (Apache, Nginx, etc.)
- Pi-hole configurado e funcionando
- Token de API do Pi-hole

## Instalação

1. **Clone ou baixe os arquivos** para o diretório do seu servidor web:
   ```
   index.php
   style.css
   api.php
   ```

2. **Configure as credenciais** no arquivo `api.php`:
   - IP do Pi-hole: `************` (já configurado)
   - Token de API: `MAFHUcZbmK4ulb42wIXPUUd+hjTv1pTMgqduUfG7daI=` (já configurado)

3. **Acesse a dashboard** através do navegador:
   ```
   http://seu-servidor/DashboardPihole/
   ```

## Configuração do Pi-hole

Para obter o token de API do Pi-hole:

1. Acesse a interface web do Pi-hole
2. Vá em **Settings** > **API / Web interface**
3. Clique em **Show API token**
4. Copie o token gerado

## Estrutura dos Arquivos

- **index.php**: Página principal da dashboard com HTML e JavaScript
- **style.css**: Estilos CSS responsivos e modernos
- **api.php**: Script PHP para comunicação com a API do Pi-hole
- **README.md**: Este arquivo de documentação

## Funcionalidades

### Estatísticas Principais
- Total de consultas DNS hoje
- Consultas bloqueadas hoje
- Percentual de bloqueio
- Número de domínios únicos

### Visualizações
- Gráfico de consultas ao longo do tempo
- Lista dos domínios mais bloqueados
- Lista dos clientes mais ativos
- Tipos de consultas DNS

### Status do Sistema
- Status atual do Pi-hole (ativo/inativo)
- Versão do Pi-hole
- Indicador visual de conectividade

## Personalização

### Alterar IP do Pi-hole
Edite o arquivo `api.php` e modifique a linha:
```php
define('PIHOLE_IP', 'SEU_IP_AQUI');
```

### Alterar Token de API
Edite o arquivo `api.php` e modifique a linha:
```php
define('API_TOKEN', 'SEU_TOKEN_AQUI');
```

### Intervalo de Atualização
Para alterar o intervalo de atualização automática, edite o arquivo `index.php` e modifique:
```javascript
// Atualizar a cada 30 segundos (30000 ms)
setInterval(updateDashboard, 30000);
```

## Solução de Problemas

### Dashboard não carrega dados
1. Verifique se o Pi-hole está acessível no IP configurado
2. Confirme se o token de API está correto
3. Verifique os logs do servidor web para erros PHP

### Erro de conexão
1. Teste a conectividade: `ping ************`
2. Verifique se a API do Pi-hole está habilitada
3. Confirme se não há firewall bloqueando a conexão

### Dados não atualizam
1. Verifique o console do navegador para erros JavaScript
2. Confirme se o arquivo `api.php` está funcionando corretamente
3. Teste acessando diretamente: `http://seu-servidor/api.php?endpoint=summary`

## Tecnologias Utilizadas

- **HTML5**: Estrutura da página
- **CSS3**: Estilos modernos com gradientes e animações
- **JavaScript**: Interatividade e chamadas AJAX
- **PHP**: Backend para comunicação com Pi-hole
- **Chart.js**: Gráficos interativos
- **Font Awesome**: Ícones

## Segurança

- O token de API é usado apenas no backend (PHP)
- Validação de entrada nos endpoints
- Headers de segurança configurados
- Timeout nas requisições para evitar travamentos

## Licença

Este projeto é de código aberto e pode ser usado livremente.

## Suporte

Para problemas ou sugestões, verifique:
1. Se o Pi-hole está funcionando corretamente
2. Se as configurações de IP e token estão corretas
3. Se o servidor web tem permissões adequadas
4. Os logs do servidor para erros específicos
