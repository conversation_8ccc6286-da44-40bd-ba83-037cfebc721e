<?php
// Teste da nova API baseada na documentação oficial
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Teste da Nova API Pi-hole</h1>";

$pihole_ip = '************';
$api_token = 'MAFHUcZbmK4ulb42wIXPUUd+hjTv1pTMgqduUfG7daI=';
$base_url = "http://$pihole_ip/api";

echo "<h2>Informações do Sistema:</h2>";
echo "PHP Version: " . PHP_VERSION . "<br>";
echo "cURL disponível: " . (function_exists('curl_init') ? 'Sim' : 'Não') . "<br>";
echo "Base URL: $base_url<br>";

// Função para fazer requisições
function testApiEndpoint($url, $token, $description) {
    echo "<h3>$description</h3>";
    echo "URL: " . htmlspecialchars($url) . "<br>";
    
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 10,
        CURLOPT_CONNECTTIMEOUT => 5,
        CURLOPT_HTTPHEADER => [
            'Accept: application/json',
            'X-Pi-hole-Authenticate: ' . $token
        ]
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($response === false || !empty($error)) {
        echo "❌ Erro cURL: " . htmlspecialchars($error) . "<br>";
        return false;
    }
    
    echo "Status HTTP: $httpCode<br>";
    
    if ($httpCode >= 200 && $httpCode < 300) {
        echo "✅ Sucesso!<br>";
        echo "Resposta: " . htmlspecialchars(substr($response, 0, 300)) . "...<br>";
        
        $data = json_decode($response, true);
        if ($data && json_last_error() === JSON_ERROR_NONE) {
            echo "✅ JSON válido<br>";
            echo "<pre>" . htmlspecialchars(json_encode($data, JSON_PRETTY_PRINT)) . "</pre>";
        } else {
            echo "❌ JSON inválido: " . json_last_error_msg() . "<br>";
        }
        return true;
    } else {
        echo "❌ Erro HTTP: $httpCode<br>";
        echo "Resposta: " . htmlspecialchars($response) . "<br>";
        return false;
    }
}

// Testes dos endpoints principais
$endpoints = [
    '/dns/blocking' => 'Status do bloqueio DNS',
    '/stats/summary' => 'Resumo das estatísticas',
    '/stats/top_clients' => 'Top clientes',
    '/stats/top_blocked' => 'Top domínios bloqueados',
    '/versions' => 'Informações de versão'
];

foreach ($endpoints as $endpoint => $description) {
    $url = $base_url . $endpoint;
    testApiEndpoint($url, $api_token, $description);
    echo "<hr>";
}

echo "<h2>Teste da nossa API proxy:</h2>";

// Testar nossa API proxy
$proxy_endpoints = ['test', 'status', 'summary', 'debug'];

foreach ($proxy_endpoints as $endpoint) {
    echo "<h3>Testando endpoint: $endpoint</h3>";
    $url = "http://localhost/DashboardPihole/api.php?endpoint=$endpoint";
    echo "URL: $url<br>";
    
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 10
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($response === false || !empty($error)) {
        echo "❌ Erro: " . htmlspecialchars($error) . "<br>";
    } else {
        echo "Status: $httpCode<br>";
        echo "Resposta: " . htmlspecialchars(substr($response, 0, 200)) . "...<br>";
        
        $data = json_decode($response, true);
        if ($data && json_last_error() === JSON_ERROR_NONE) {
            echo "<pre>" . htmlspecialchars(json_encode($data, JSON_PRETTY_PRINT)) . "</pre>";
        }
    }
    echo "<hr>";
}
?>
